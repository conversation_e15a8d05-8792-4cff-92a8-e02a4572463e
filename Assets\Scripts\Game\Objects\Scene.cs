using UnityEngine;
using SmartVertex.Tools;
using Game.Managers;

namespace Game.Objects
{
    /// <summary>
    /// Represents a scene with walkable grid areas and spawn points for props.
    /// </summary>
    public class Scene : MonoBehaviour
    {
        #region Fields
        [SerializeField] private SceneGrid[] walkableCells;
        [SerializeField] private Transform[] smallPropSpawnPoints;
        [SerializeField] private Transform[] largePropSpawnPoints;

        [Header("Grid Visualization")]
        [SerializeField] private bool showGridGizmos = true;
        [SerializeField] private Color walkableColor = Color.green;
        [SerializeField] private Color nonWalkableColor = Color.red;
        [SerializeField] private Color gridLineColor = Color.white;
        [SerializeField] private float gizmoAlpha = 0.3f;
        [SerializeField] private GridUtility.GridPlane gridPlane = GridUtility.GridPlane.XY;
        #endregion

        #region Properties
        /// <summary>
        /// Gets the walkable cells for this scene.
        /// </summary>
        public SceneGrid[] WalkableCells => walkableCells;

        /// <summary>
        /// Gets the small prop spawn points.
        /// </summary>
        public Transform[] SmallPropSpawnPoints => smallPropSpawnPoints;

        /// <summary>
        /// Gets the large prop spawn points.
        /// </summary>
        public Transform[] LargePropSpawnPoints => largePropSpawnPoints;
        #endregion

        #region Gizmo Drawing
        /// <summary>
        /// Draws gizmos in the editor to visualize the walkable grid areas.
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!showGridGizmos || walkableCells == null)
                return;

            if (!GridUtility.TryGetBounds(gameObject, out Bounds sceneBounds))
                return;

            Vector2Int gridSize = GetGridSize();
            if (gridSize.x <= 0 || gridSize.y <= 0)
                return;

            DrawGridLines(sceneBounds, gridSize);
            DrawWalkableAreas(sceneBounds, gridSize);
        }

        /// <summary>
        /// Draws the grid lines to show the cell boundaries.
        /// </summary>
        /// <param name="sceneBounds">The bounds of the scene.</param>
        /// <param name="gridSize">The size of the grid.</param>
        private void DrawGridLines(Bounds sceneBounds, Vector2Int gridSize)
        {
            Gizmos.color = gridLineColor;

            // Get grid calculation values (same as GridUtility internal logic)
            GetPlaneVectors(gridPlane, out var uAxis, out var vAxis, out var wAxis);
            var planeSize = new Vector2(Vector3.Dot(sceneBounds.size, uAxis), Vector3.Dot(sceneBounds.size, vAxis));
            var cellSize = new Vector2(planeSize.x / gridSize.x, planeSize.y / gridSize.y);
            var gridOrigin = sceneBounds.center - sceneBounds.extents;

            // Draw vertical lines (along U axis)
            for (int x = 0; x <= gridSize.x; x++)
            {
                Vector3 lineStart = gridOrigin + uAxis * (cellSize.x * x) + wAxis * (Vector3.Dot(sceneBounds.size, wAxis) * 0.5f);
                Vector3 lineEnd = lineStart + vAxis * planeSize.y;

                Gizmos.DrawLine(lineStart, lineEnd);
            }

            // Draw horizontal lines (along V axis)
            for (int y = 0; y <= gridSize.y; y++)
            {
                Vector3 lineStart = gridOrigin + vAxis * (cellSize.y * y) + wAxis * (Vector3.Dot(sceneBounds.size, wAxis) * 0.5f);
                Vector3 lineEnd = lineStart + uAxis * planeSize.x;

                Gizmos.DrawLine(lineStart, lineEnd);
            }
        }

        /// <summary>
        /// Draws the walkable areas as colored cubes.
        /// </summary>
        /// <param name="sceneBounds">The bounds of the scene.</param>
        /// <param name="gridSize">The size of the grid.</param>
        private void DrawWalkableAreas(Bounds sceneBounds, Vector2Int gridSize)
        {
            Vector3 cellSize = GetCellSize(sceneBounds, gridSize);

            foreach (var cell in walkableCells)
            {
                Color cellColor = cell.walkable ? walkableColor : nonWalkableColor;
                cellColor.a = gizmoAlpha;
                Gizmos.color = cellColor;

                // Draw each cell in the range
                for (int x = cell.xStart; x <= cell.xEnd; x++)
                {
                    for (int y = cell.yStart; y <= cell.yEnd; y++)
                    {
                        Vector3 cellCenter = GridUtility.GetWorldPosition(sceneBounds, gridSize, new Vector2Int(x, y), gridPlane);
                        Gizmos.DrawCube(cellCenter, cellSize);
                    }
                }
            }
        }

        /// <summary>
        /// Gets the size of individual grid cells.
        /// </summary>
        /// <param name="sceneBounds">The bounds of the scene.</param>
        /// <param name="gridSize">The size of the grid.</param>
        /// <returns>The size of each cell.</returns>
        private Vector3 GetCellSize(Bounds sceneBounds, Vector2Int gridSize)
        {
            GetPlaneVectors(gridPlane, out var uAxis, out var vAxis, out var wAxis);
            var planeSize = new Vector2(Vector3.Dot(sceneBounds.size, uAxis), Vector3.Dot(sceneBounds.size, vAxis));
            var cellSize = new Vector2(planeSize.x / gridSize.x, planeSize.y / gridSize.y);

            return uAxis * cellSize.x + vAxis * cellSize.y + wAxis * 0.1f; // Small depth for visibility
        }

        /// <summary>
        /// Gets the primary (u), secondary (v), and normal (w) axes for a given plane.
        /// This mirrors the private method in GridUtility.
        /// </summary>
        /// <param name="plane">The grid plane.</param>
        /// <param name="u">Primary axis (grid X).</param>
        /// <param name="v">Secondary axis (grid Y).</param>
        /// <param name="w">Normal axis (depth).</param>
        private void GetPlaneVectors(GridUtility.GridPlane plane, out Vector3 u, out Vector3 v, out Vector3 w)
        {
            switch (plane)
            {
                case GridUtility.GridPlane.XZ:
                    u = Vector3.right;
                    v = Vector3.forward;
                    w = Vector3.up;
                    break;
                case GridUtility.GridPlane.YZ:
                    u = Vector3.up;
                    v = Vector3.forward;
                    w = Vector3.right;
                    break;
                case GridUtility.GridPlane.XY:
                default:
                    u = Vector3.right;
                    v = Vector3.up;
                    w = Vector3.forward;
                    break;
            }
        }

        /// <summary>
        /// Gets the axis vector for the specified grid plane.
        /// </summary>
        /// <param name="plane">The grid plane.</param>
        /// <param name="isPrimaryAxis">True for primary axis (u), false for secondary axis (v).</param>
        /// <returns>The axis vector.</returns>
        private Vector3 GetAxisVector(GridUtility.GridPlane plane, bool isPrimaryAxis)
        {
            switch (plane)
            {
                case GridUtility.GridPlane.XZ:
                    return isPrimaryAxis ? Vector3.right : Vector3.forward;
                case GridUtility.GridPlane.YZ:
                    return isPrimaryAxis ? Vector3.up : Vector3.forward;
                case GridUtility.GridPlane.XY:
                default:
                    return isPrimaryAxis ? Vector3.right : Vector3.up;
            }
        }

        /// <summary>
        /// Gets the grid size from the ObjectManager or uses a default value.
        /// </summary>
        /// <returns>The grid size.</returns>
        private Vector2Int GetGridSize()
        {
            if (ObjectManager.Instance != null)
                return ObjectManager.Instance.SceneGridSize;

            // Default fallback if ObjectManager is not available
            return new Vector2Int(14, 8);
        }

        /// <summary>
        /// Tests the grid utility by checking if a world position converts back to the same grid coordinates.
        /// Call this method in the inspector or during debugging.
        /// </summary>
        [ContextMenu("Test Grid Utility")]
        private void TestGridUtility()
        {
            if (!GridUtility.TryGetBounds(gameObject, out Bounds bounds))
            {
                Debug.LogError("Could not get bounds for grid utility test");
                return;
            }

            Vector2Int gridSize = GetGridSize();
            Debug.Log($"Testing grid utility with bounds: {bounds}, grid size: {gridSize}");

            // Test a few grid positions
            for (int x = 0; x < Mathf.Min(3, gridSize.x); x++)
            {
                for (int y = 0; y < Mathf.Min(3, gridSize.y); y++)
                {
                    Vector2Int originalCoords = new Vector2Int(x, y);
                    Vector3 worldPos = GridUtility.GetWorldPosition(bounds, gridSize, originalCoords, gridPlane);
                    Vector2Int convertedCoords = GridUtility.GetGridCoordinates(bounds, gridSize, worldPos, gridPlane);

                    bool matches = originalCoords == convertedCoords;
                    Debug.Log($"Grid ({x},{y}) -> World {worldPos} -> Grid ({convertedCoords.x},{convertedCoords.y}) - Match: {matches}");

                    if (!matches)
                    {
                        Debug.LogWarning($"Grid utility conversion mismatch at ({x},{y})!");
                    }
                }
            }
        }
        #endregion
    }

    /// <summary>
    /// Represents a rectangular area in the scene grid that can be walkable or non-walkable.
    /// </summary>
    [System.Serializable]
    public class SceneGrid
    {
        [Tooltip("Whether this grid area is walkable")]
        public bool walkable = true;

        [Tooltip("Starting X coordinate (inclusive)")]
        public int xStart;

        [Tooltip("Starting Y coordinate (inclusive)")]
        public int yStart;

        [Tooltip("Ending X coordinate (inclusive)")]
        public int xEnd;

        [Tooltip("Ending Y coordinate (inclusive)")]
        public int yEnd;

        /// <summary>
        /// Checks if the specified grid coordinates are within this grid area.
        /// </summary>
        /// <param name="x">The X coordinate to check.</param>
        /// <param name="y">The Y coordinate to check.</param>
        /// <returns>True if the coordinates are within this grid area.</returns>
        public bool Contains(int x, int y)
        {
            return x >= xStart && x <= xEnd && y >= yStart && y <= yEnd;
        }

        /// <summary>
        /// Gets the number of cells in this grid area.
        /// </summary>
        public int CellCount => (xEnd - xStart + 1) * (yEnd - yStart + 1);
    }
}