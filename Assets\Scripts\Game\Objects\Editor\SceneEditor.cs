using UnityEngine;
using UnityEditor;
using Game.Managers;

namespace Game.Objects.Editor
{
    /// <summary>
    /// Custom editor for the Scene component to provide better visualization and editing tools.
    /// </summary>
    [CustomEditor(typeof(Scene))]
    public class SceneEditor : UnityEditor.Editor
    {
        private Scene sceneComponent;
        private SerializedProperty walkableCellsProp;
        private SerializedProperty smallPropSpawnPointsProp;
        private SerializedProperty largePropSpawnPointsProp;
        private SerializedProperty showGridGizmosProp;
        private SerializedProperty walkableColorProp;
        private SerializedProperty nonWalkableColorProp;
        private SerializedProperty gridLineColorProp;
        private SerializedProperty gizmoAlphaProp;
        private SerializedProperty gridPlaneProp;

        private void OnEnable()
        {
            sceneComponent = (Scene)target;

            walkableCellsProp = serializedObject.FindProperty("walkableCells");
            smallPropSpawnPointsProp = serializedObject.FindProperty("smallPropSpawnPoints");
            largePropSpawnPointsProp = serializedObject.FindProperty("largePropSpawnPoints");
            showGridGizmosProp = serializedObject.FindProperty("showGridGizmos");
            walkableColorProp = serializedObject.FindProperty("walkableColor");
            nonWalkableColorProp = serializedObject.FindProperty("nonWalkableColor");
            gridLineColorProp = serializedObject.FindProperty("gridLineColor");
            gizmoAlphaProp = serializedObject.FindProperty("gizmoAlpha");
            gridPlaneProp = serializedObject.FindProperty("gridPlane");
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Scene Configuration", EditorStyles.boldLabel);

            // Walkable Cells
            EditorGUILayout.PropertyField(walkableCellsProp, new GUIContent("Walkable Cells", "Define walkable and non-walkable areas in the scene grid"));

            EditorGUILayout.Space();

            // Spawn Points
            EditorGUILayout.PropertyField(smallPropSpawnPointsProp, new GUIContent("Small Prop Spawn Points", "Transform points where small props can be spawned"));
            EditorGUILayout.PropertyField(largePropSpawnPointsProp, new GUIContent("Large Prop Spawn Points", "Transform points where large props can be spawned"));

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Grid Visualization", EditorStyles.boldLabel);

            // Grid Visualization Settings
            EditorGUILayout.PropertyField(showGridGizmosProp, new GUIContent("Show Grid Gizmos", "Enable/disable grid visualization in the scene view"));

            if (showGridGizmosProp.boolValue)
            {
                EditorGUI.indentLevel++;
                EditorGUILayout.PropertyField(gridPlaneProp, new GUIContent("Grid Plane", "The plane on which the grid is projected"));
                EditorGUILayout.PropertyField(walkableColorProp, new GUIContent("Walkable Color", "Color for walkable grid cells"));
                EditorGUILayout.PropertyField(nonWalkableColorProp, new GUIContent("Non-Walkable Color", "Color for non-walkable grid cells"));
                EditorGUILayout.PropertyField(gridLineColorProp, new GUIContent("Grid Line Color", "Color for grid lines"));
                EditorGUILayout.PropertyField(gizmoAlphaProp, new GUIContent("Gizmo Alpha", "Transparency of the grid cell gizmos"));
                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space();

            // Helper buttons
            if (GUILayout.Button("Add Walkable Area"))
            {
                AddWalkableArea(true);
            }

            if (GUILayout.Button("Add Non-Walkable Area"))
            {
                AddWalkableArea(false);
            }

            EditorGUILayout.Space();

            // Grid info
            if (sceneComponent != null)
            {
                EditorGUILayout.LabelField("Grid Information", EditorStyles.boldLabel);
                EditorGUI.BeginDisabledGroup(true);

                var gridSize = GetGridSize();
                EditorGUILayout.Vector2IntField("Grid Size", gridSize);

                if (sceneComponent.WalkableCells != null)
                {
                    int totalCells = 0;
                    int walkableCellCount = 0;

                    foreach (var cell in sceneComponent.WalkableCells)
                    {
                        int cellCount = cell.CellCount;
                        totalCells += cellCount;
                        if (cell.walkable)
                            walkableCellCount += cellCount;
                    }

                    EditorGUILayout.IntField("Total Defined Cells", totalCells);
                    EditorGUILayout.IntField("Walkable Cells", walkableCellCount);
                    EditorGUILayout.IntField("Non-Walkable Cells", totalCells - walkableCellCount);
                }

                EditorGUI.EndDisabledGroup();
            }

            serializedObject.ApplyModifiedProperties();
        }

        /// <summary>
        /// Adds a new walkable area to the scene.
        /// </summary>
        /// <param name="isWalkable">Whether the new area should be walkable.</param>
        private void AddWalkableArea(bool isWalkable)
        {
            var gridSize = GetGridSize();

            // Create a new SceneGrid
            var newGrid = new SceneGrid
            {
                walkable = isWalkable,
                xStart = 0,
                yStart = 0,
                xEnd = Mathf.Max(0, gridSize.x - 1),
                yEnd = Mathf.Max(0, gridSize.y - 1)
            };

            // Add to the array
            walkableCellsProp.arraySize++;
            var newElement = walkableCellsProp.GetArrayElementAtIndex(walkableCellsProp.arraySize - 1);

            newElement.FindPropertyRelative("walkable").boolValue = newGrid.walkable;
            newElement.FindPropertyRelative("xStart").intValue = newGrid.xStart;
            newElement.FindPropertyRelative("yStart").intValue = newGrid.yStart;
            newElement.FindPropertyRelative("xEnd").intValue = newGrid.xEnd;
            newElement.FindPropertyRelative("yEnd").intValue = newGrid.yEnd;

            serializedObject.ApplyModifiedProperties();
        }

        /// <summary>
        /// Gets the grid size from the ObjectManager or uses a default value.
        /// </summary>
        /// <returns>The grid size.</returns>
        private Vector2Int GetGridSize()
        {
            if (ObjectManager.Instance != null)
                return ObjectManager.Instance.SceneGridSize;

            // Default fallback if ObjectManager is not available
            return new Vector2Int(14, 8);
        }
    }
}
