using System;
using System.Collections;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to wait for a specified duration before continuing execution.
    /// </summary>
    public class WaitCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the wait command.</summary>
        public WaitParams Parameters { get; }

        /// <summary>Creates a new WaitCommand.</summary>
        /// <param name="parameters">Wait parameters.</param>
        public WaitCommand(WaitParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            if (Parameters.duration <= 0f)
            {
                Debug.LogWarning($"WaitCommand: Invalid duration '{Parameters.duration}'. Duration must be greater than 0.");
                yield break;
            }

            yield return new WaitForSeconds(Parameters.duration);
        }
    }

    /// <summary>Parameters for Wait command.</summary>
    [Serializable]
    public struct WaitParams
    {
        /// <summary>The duration to wait in seconds.</summary>
        public float duration;
    }
}
