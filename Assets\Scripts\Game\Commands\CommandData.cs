using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SmartVertex.Tools;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Types of commands supported by the command system.
    /// </summary>
    public enum CommandType
    {
        /// <summary>Unknown command type.</summary>
        Unknown = 0,
        /// <summary>Moves an object vertically or horizontally based on predefined grid units.</summary>
        Move = 1,
        /// <summary>Rotates an object.</summary>
        Rotate = 2,
        /// <summary>Scales an object in 2d coordinates.</summary>
        Scale = 3,
        /// <summary>Create anything in the defined scene.</summary>
        Create = 4,
        /// <summary>Destroys anything in the defined scene.</summary>
        Destroy = 5,
        /// <summary>Composite command (contains child commands).</summary>
        Composite = 6,
        /// <summary>Generates speech and subtitle for the specified character.</summary>
        Dialogue = 7,
        /// <summary>Focuses camera on specified object.</summary>
        CameraFocus = 8,
        /// <summary>Plays a predefined animation on the specified object.</summary>
        PlayAnimation = 9,
        /// <summary>Plays a sound effect or music track globally.</summary>
        PlaySound = 10,
        /// <summary>Centers the camera to show all characters in view.</summary>
        CenterCamera = 11,
        /// <summary>Sets the camera distance from the target.</summary>
        SetCameraDistance = 12,
        /// <summary>Sets the camera shake type and intensity.</summary>
        SetCameraShake = 13,
        /// <summary>Sets the post-processing profile.</summary>
        SetPostProcessingProfile = 14,
        /// <summary>Waits for a specified duration before continuing.</summary>
        Wait = 15,
    }

    /// <summary>
    /// Data container for a sequence of commands.
    /// </summary>
    [Serializable]
    public class CommandSequenceData
    {
        /// <summary>Title of the command sequence.</summary>
        public string title;
        /// <summary>List of commands in the sequence.</summary>
        public List<CommandData> commands;
    }

    /// <summary>
    /// Data container for a single command.
    /// </summary>
    [JsonConverter(typeof(CommandDataConverter))]
    public class CommandData
    {
        /// <summary>Type of the command.</summary>
        public CommandType type;
        /// <summary>Parameters for the command.</summary>
        public object parameters;
        /// <summary>Child commands (for composite commands).</summary>
        public List<CommandData> commands;
    }

    /// <summary>
    /// Custom JSON converter for CommandData, supporting fuzzy type matching.
    /// </summary>
    public class CommandDataConverter : JsonConverter<CommandData>
    {
        private const int MaxLevenshteinDistance = 2;

        public override CommandData ReadJson(JsonReader reader, Type objectType, CommandData existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            JObject jo = JObject.Load(reader);
            string typeStr = jo["type"]?.Value<string>();

            if (string.IsNullOrEmpty(typeStr))
            {
                Debug.LogError($"Command object is missing required 'type' field: {jo}");
            }

            CommandType matchedType = FindBestMatchingCommandType(typeStr);
            if (matchedType == CommandType.Unknown)
            {
                Debug.LogError($"Unknown command type '{typeStr}' and no close match found. Please check spelling.");
            }

            var commandData = new CommandData { type = matchedType };

            JToken parametersToken = jo["parameters"];
            if (parametersToken != null && parametersToken.Type != JTokenType.Null)
            {
                switch (matchedType)
                {
                    case CommandType.Move:
                        commandData.parameters = parametersToken.ToObject<MoveParams>(serializer);
                        break;
                    case CommandType.Dialogue:
                        commandData.parameters = parametersToken.ToObject<DialogueParams>(serializer);
                        break;
                    case CommandType.CameraFocus:
                        commandData.parameters = parametersToken.ToObject<FocusParams>(serializer);
                        break;
                    case CommandType.Rotate:
                        commandData.parameters = parametersToken.ToObject<RotateParams>(serializer);
                        break;
                    case CommandType.Scale:
                        commandData.parameters = parametersToken.ToObject<ScaleParams>(serializer);
                        break;
                    case CommandType.Create:
                        commandData.parameters = parametersToken.ToObject<CreateParams>(serializer);
                        break;
                    case CommandType.Destroy:
                        commandData.parameters = parametersToken.ToObject<DestroyParams>(serializer);
                        break;
                    case CommandType.PlayAnimation:
                        commandData.parameters = parametersToken.ToObject<PlayAnimationParams>(serializer);
                        break;
                    case CommandType.PlaySound:
                        commandData.parameters = parametersToken.ToObject<PlaySoundParams>(serializer);
                        break;
                    case CommandType.CenterCamera:
                        commandData.parameters = parametersToken.ToObject<CenterCameraParams>(serializer);
                        break;
                    case CommandType.SetCameraDistance:
                        commandData.parameters = parametersToken.ToObject<SetCameraDistanceParams>(serializer);
                        break;
                    case CommandType.SetCameraShake:
                        commandData.parameters = parametersToken.ToObject<SetCameraShakeParams>(serializer);
                        break;
                    case CommandType.SetPostProcessingProfile:
                        commandData.parameters = parametersToken.ToObject<SetPostProcessingParams>(serializer);
                        break;
                    case CommandType.Wait:
                        commandData.parameters = parametersToken.ToObject<WaitParams>(serializer);
                        break;
                }
            }

            JToken childrenToken = jo["commands"];
            if (childrenToken != null && childrenToken.Type == JTokenType.Array)
            {
                commandData.commands = childrenToken.ToObject<List<CommandData>>(serializer);
            }

            return commandData;
        }

        private CommandType FindBestMatchingCommandType(string inputType)
        {
            string[] enumNames = Enum.GetNames(typeof(CommandType));
            int lowestDistance = int.MaxValue;
            string bestMatch = null;

            foreach (string enumName in enumNames)
            {
                if (enumName == nameof(CommandType.Unknown)) continue;

                int distance = StringUtility.Levenshtein(inputType.ToLowerInvariant(), enumName.ToLowerInvariant());

                if (distance == 0) return (CommandType)Enum.Parse(typeof(CommandType), enumName);
                if (distance < lowestDistance)
                {
                    lowestDistance = distance;
                    bestMatch = enumName;
                }
            }

            if (bestMatch != null && lowestDistance <= MaxLevenshteinDistance)
            {
                Debug.LogWarning($"Fuzzy Match: Interpreting command type '{inputType}' as '{bestMatch}' (Levenshtein distance: {lowestDistance}).");
                return (CommandType)Enum.Parse(typeof(CommandType), bestMatch);
            }

            return CommandType.Unknown;
        }

        public override void WriteJson(JsonWriter writer, CommandData value, JsonSerializer serializer)
        {
            // Not implemented (not needed for this use case)
        }
    }
}
